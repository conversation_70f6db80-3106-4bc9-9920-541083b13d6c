import { notFound } from "next/navigation";
import { getArticleBySlug, getAllArticles } from "@/lib/articles";
import Header from "@/components/header";
import Footer from "@/components/footer";
import { marked } from "marked";

// Configure marked for better markdown rendering
marked.setOptions({
  gfm: true,
  breaks: true,
});

// Simple markdown content renderer
function MarkdownContent({ content }: { content: string }) {
  const htmlContent = marked(content);
  return (
    <div
      className="prose prose-invert max-w-none"
      dangerouslySetInnerHTML={{ __html: htmlContent }}
    />
  );
}

export async function generateStaticParams() {
  const articles = getAllArticles();
  return articles.map((article) => ({
    slug: article.slug,
  }));
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  try {
    const { slug } = await params;
    const article = getArticleBySlug(slug);

    if (!article) {
      return {
        title: "Article Not Found",
      };
    }

    return {
      title: article.title,
      description: article.excerpt,
    };
  } catch (error) {
    console.error("Error in generateMetadata:", error);
    return {
      title: "Error Loading Article",
    };
  }
}

export default async function ArticlePage({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  try {
    const { slug } = await params;
    const article = getArticleBySlug(slug);

    if (!article) {
      notFound();
    }

    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1 container py-10">
          <article className="max-w-3xl mx-auto">
            <header className="mb-8">
              <h1 className="text-4xl font-bold mb-4">{article.title}</h1>
              <p className="text-muted-foreground">
                Published on{" "}
                {new Date(article.publishedAt).toLocaleDateString()}
              </p>
            </header>

            <MarkdownContent content={article.content} />
          </article>
        </main>
        <Footer />
      </div>
    );
  } catch (error) {
    console.error("Error in ArticlePage:", error);
    throw error;
  }
}
