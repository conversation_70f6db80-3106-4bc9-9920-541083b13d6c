---
title: "Initial (vibe) commit"
excerpt: "How motyl.dev was created in less than an hour."
publishedAt: "2025-05-26"
slug: "initial-vibe-commit"
---

### TLDR:

in this article I described how I created motyl.dev in less than an hour using vercel v0, what worked and what didn't.

### What is V0?

V0 is a platform that allows you to build and deploy web applications using AI. It's a bit like ChatGPT, but for building web applications. It's in early stages and it's not perfect, but it's a lot of fun to play with.

### How I created motyl.dev

Vercel's v0 platform recently provided an extraordinary "vibe coding" experience, allowing me to build a working prototype of a personalized frontend developer newsletter landing page with a CMS in less than an hour. This rapid development was at least 10x faster than traditional manual coding, demonstrating the immense potential of AI-driven development.

### The Genesis of a Prototype

My journey with v0 began with a simple request:

<blockquote>
  a landing page to collect email addresses for a newsletter aimed at frontend
  developers. I want a professional platform delivering expert-level articles.
</blockquote>

V0's initial response provided a solid foundation, featuring a clean design, an email collection form, and sections for value proposition and testimonials.

The real magic, however, started when I began personalizing the experience. As <PERSON><PERSON><PERSON><PERSON>, a Principal Software Developer and Architect with a passion for Functional Programming, Domain Driven Design, and Software Craftsmanship, I wanted the page to reflect my unique identity and expertise. V0 seamlessly integrated these elements, transforming the generic template into a bespoke representation of my professional ethos, complete with a philosophy on raising the bar of professional software development.

From Concept to Code: Iterative Refinement
The iterative nature of the conversation with v0 was where its power truly shone. I continued to push the boundaries, introducing new concepts and challenging its initial outputs:

Shifting Focus to Agentic AI: I pivoted the content from Domain-Driven Design to Agentic AI, a burgeoning field I'm actively exploring. V0 swiftly adapted, demonstrating its flexibility in incorporating significant conceptual changes.
Introducing a CMS: A critical requirement was the ability to manage articles. V0 ingeniously implemented a full-fledged Admin CMS with a login page, an article editor supporting Markdown and code snippets (with VS Code-like highlighting), and dynamic article pages. This was a monumental addition, drastically expanding the scope of the project.
Design Evolution: The aesthetic underwent a significant transformation. I requested a dark theme with purple accents and a subtle butterfly motif in the background, aiming for a modern, tech-forward, yet elegant look. V0 delivered, not only applying the desired palette and imagery but also ensuring design consistency and alignment across various components.
Addressing Errors and Refining Functionality: No development process is without its hiccups. When the /admin page initially presented a "Page not found" error or redirects, v0 diligently diagnosed and rectified the issues, refining the authentication flow and ensuring data persistence using localStorage. This demonstrated its ability to debug and learn from its own outputs.
Email Notification Integration: A crucial feature for a newsletter is subscriber notification. I requested an email to be sent to my professional address (<EMAIL>) upon new subscriptions. V0 integrated this seamlessly, utilizing the Resend API and navigating through several API-related errors (400, 403, and testing mode limitations) until the system was fully functional, demonstrating remarkable persistence and problem-solving.
Content and Tone Adjustments: Even the language and tone of the landing page were subject to refinement. I opted for a more direct, approachable style, focusing on "upskilling frontend developers" and leveraging my "20+ years of hands-on experience," moving away from formal "Agentic AI" jargon. V0 readily adopted this conversational shift, highlighting its capacity for nuanced content generation.
Streamlining the Prototype: Finally, to focus solely on the newsletter aspect, I requested the removal of the entire admin section and associated APIs. V0 executed this cleanup flawlessly, leaving a lean, focused newsletter landing page.
The Power of "Vibe Coding"
The term "vibe coding" perfectly encapsulates the experience. It wasn't about writing lines of code; it was about expressing an intent, a "vibe," and letting the AI translate that into a functional application. The conversation flowed naturally, almost as if collaborating with an extremely proficient and endlessly patient developer. Each iteration built upon the last, progressively bringing the vision to life.

This accelerated development cycle, from an initial concept to a fully functional, aesthetically pleasing, and personalized prototype in less than an hour, is a testament to the transformative power of platforms like Vercel's v0. It significantly reduces the barrier to entry for creators and developers, allowing for rapid experimentation and iteration.

In upcoming articles, I'll delve deeper into specific aspects of this process, providing more technical insights and exploring the implications of AI-driven development for the future of software craftsmanship.
