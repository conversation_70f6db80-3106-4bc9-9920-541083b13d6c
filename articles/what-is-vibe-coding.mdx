---
title: "What is Vibe Coding?"
excerpt: "Discover the philosophy behind vibe coding and how it transforms the way we approach software development."
publishedAt: "2025-05-26"
slug: "what-is-vibe-coding"
---

# This project is example of vibe coding!

Vibe coding is (in theory) about finding your natural rhythm and flow when writing code, focusing on intuition and craftsmanship over rigid rules.

I wrote motyl.dev in vercel v0 and I will describe in following articles how I did it. For now I can say it works, but it's not perfect (yet) and my developer expirience was already needed to go to this point.

... to be continued ;) ...
